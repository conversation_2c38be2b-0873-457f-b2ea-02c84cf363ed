import { EarthquakePredictionTool } from "@/components/earthquake-prediction-tool"
import { APIDialog } from "@/components/api-dialog"
import { ModelInfoDialog } from "@/components/model-info-dialog"

const apiInfo = {
  toolName: "Earthquake Prediction Tool",
  endpoint: "https://pga-curve-715610578807.us-central1.run.app",
  requestExample: {
    target_latitude: -15.02,
    target_longitude: -72.15,
    window: 1,
    Vs30: 760
  },
  responseExample: {
    data: [
      {
        pga_value: 0.1,
        probability_of_exceedance: 85.2
      },
      {
        pga_value: 0.2,
        probability_of_exceedance: 45.8
      },
      {
        pga_value: 0.3,
        probability_of_exceedance: 22.1
      }
    ],
    metadata: {
      target_latitude: -15.02,
      target_longitude: -72.15,
      window: 1,
      vs30: 760
    }
  }
}

const modelInfo = {
  title: "Earthquake Prediction Tool",
  description: "This tool generates probabilistic earthquake curves showing the relationship between Peak Ground Acceleration (PGA) and probability of exceedance for a specific location and time window.",
  type: "tool" as const,
  purpose: [
    "Assess seismic risk for insurance underwriting",
    "Generate probabilistic earthquake hazard curves",
    "Evaluate ground motion intensity probabilities",
    "Support property risk assessment decisions"
  ],
  methodology: [
    "Uses seismic hazard models based on geological data",
    "Incorporates site-specific soil conditions (Vs30 values)",
    "Applies probabilistic seismic hazard analysis (PSHA)",
    "Leverages USGS geological survey data when Vs30 not specified"
  ],
  outputs: [
    "Probabilistic curve of PGA vs exceedance probability",
    "Peak Ground Acceleration values in units of gravity (g)",
    "Probability percentages for different acceleration levels",
    "Interactive visualization of seismic risk profile"
  ]
}

export default function EarthquakePredictionPage() {
  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold">Earthquake Prediction Tool</h1>
        <div className="flex gap-2">
          <ModelInfoDialog {...modelInfo} />
          <APIDialog {...apiInfo} />
        </div>
      </div>
      <EarthquakePredictionTool />
    </div>
  )
}
