"use client";

import Link from "next/link";
import {
  FileText,
  Briefcase,
  Users,
  ChevronDown,
  PenToolIcon as Wrench,
  Search,
  AlertTriangle,
  Settings,
  LogOut,
  TrendingDown,
  GitBranch,
  PlayCircle,
  BarChart,
  LayoutDashboard,
  ShoppingBag,
  FileCheck,
  Bot,
  Car,
  Home,
  ListIcon as List,
} from "lucide-react";
import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { signOut } from "next-auth/react";
import type { Session } from "next-auth";
import type { UserRole } from "@/types/user";

const MenuItems = [
  {
    label: "ML Models",
    items: [
      {
        name: "Auto",
        icon: Car,
        subItems: [
          { name: "Claims Segmentation", href: "/dashboard/models/claims-segmentation" },
          { name: "Total Loss", href: "/dashboard/models/total-loss" },
          { name: "Car Fraud Detection", href: "/dashboard/models/custom-fraud-detection" },
        ],
      },
      {
        name: "Property",
        icon: Home,
        subItems: [
          { name: "Property Risk Assessment", href: "/dashboard/models/property-risk" },
          { name: "Natural Disaster Risk", href: "/dashboard/models/disaster-risk" },
        ],
      },
      {
        name: "Underwriting",
        icon: FileCheck,
        subItems: [
          { name: "CLV", href: "/dashboard/models/clv" },
          { name: "Churn", href: "/dashboard/models/churn" },
          { name: "Risk Scoring", href: "/dashboard/models/risk-scoring" },
        ],
      },
      {
        name: "General",
        icon: Briefcase,
        subItems: [
          { name: "Custom Models", href: "/dashboard/models/custom" },
          { name: "Market Analysis", href: "/dashboard/models/market-analysis" },
        ],
      },
    ],
  },
  {
    label: "Tools",
    items: [
      {
        name: "Auto",
        icon: Car,
        subItems: [
          { name: "Car Appraisal", href: "/dashboard/tools/car-appraisal", disabled: true },
          { name: "Vehicle Specs", href: "/dashboard/tools/vehicle-specs" },
          { name: "Market Evaluation", href: "/dashboard/tools/market-evaluation" },
          { name: "Check Insurer", href: "/dashboard/tools/check-insurer" },
        ],
      },
      {
        name: "Property",
        icon: Home,
        subItems: [
          { name: "Home Appraisal", href: "/dashboard/tools/home-appraisal" },
          { name: "Satellite Imagery", href: "/dashboard/tools/satellite-imagery" },
          { name: "Weather", href: "/dashboard/tools/weather" },
        ],
      },
      {
        name: "Document Processing",
        icon: FileText,
        subItems: [
          { name: "OCR", href: "/dashboard/tools/ocr" },
          { name: "FNOL Speech-to-Text", href: "/dashboard/tools/fnol-speech-to-text" },
          { name: "Claim Acceptance", href: "/dashboard/tools/claim-acceptance" },
        ],
      },
      {
        name: "Investigation",
        icon: Search,
        subItems: [
          { name: "Check Debtor", href: "/dashboard/tools/check-debtor" },
          { name: "Network Analysis", href: "/dashboard/tools/network-analysis" },
          { name: "OSINT", href: "/dashboard/tools/osint" },
          { name: "Identity Check", href: "/dashboard/tools/identity-check" },
          { name: "Image Analyser", href: "/dashboard/tools/image-analyser" },
          { name: "Complex Claims Analysis", href: "/dashboard/tools/complex-claims-analysis" },
        ],
      },
      {
        name: "Underwritting",
        icon: Briefcase,
        subItems: [
          { name: "Commercial Due Diligence", href: "/dashboard/tools/commercial-due-diligence" },
        ],
      },
    ],
  },
  {
    label: "AI Workflows",
    items: [
      {
        name: "All Workflows",
        icon: GitBranch,
        href: "/dashboard/workflows",
      },
      {
        name: "My Tasks",
        icon: List,
        href: "/dashboard/workflows/my-tasks",
      },
      {
        name: "Create Workflow",
        icon: PlayCircle,
        href: "/dashboard/workflows/create",
      },
      {
        name: "Tools",
        icon: Wrench,
        href: "/dashboard/workflows/tools",
      },
      {
        name: "AI Agents",
        icon: Bot,
        href: "/dashboard/workflows/agents",
      },
    ],
  },
  {
    label: "AI Assistants",
    items: [
      {
        name: "Fraud Assistant",
        icon: AlertTriangle,
        href: "/dashboard/ai-assistants/fraud-assistant",
      },
      {
        name: "Underwritting Assistant",
        icon: FileCheck,
        href: "/dashboard/ai-assistants/fraud-assistant",
      },
      {
        name: "Appraiser Assistant",
        icon: BarChart,
        href: "/dashboard/ai-assistants/fraud-assistant",
      },
    ],
  },
  {
    label: "Analytics",
    items: [
      {
        name: "Claims",
        icon: FileText,
        subItems: [
          { name: "Dashboard", href: "/dashboard/claims" },
          { name: "Browse", href: "/dashboard/claims/browse" },
        ],
      },
      {
        name: "Appraisals",
        icon: FileCheck,
        subItems: [{ name: "Dashboard", href: "/dashboard/appraisals" }],
      },
      {
        name: "Policies",
        icon: Briefcase,
        subItems: [
          { name: "Dashboard", href: "/dashboard/policies" },
          { name: "Browse", href: "/dashboard/policies/browse" },
        ],
      },
      {
        name: "Clients",
        icon: Users,
        subItems: [
          { name: "Dashboard", href: "/dashboard/clients" },
          { name: "Browse", href: "/dashboard/clients/browse" },
        ],
      },
    ],
  },
  {
    label: "Project Management",
    items: [
      {
        name: "Fraud",
        icon: AlertTriangle,
        subItems: [
          { name: "Dashboard", href: "/dashboard/fraud" },
          { name: "Browse", href: "/dashboard/fraud/browse" },
          { name: "Settings", href: "/dashboard/fraud/settings" },
        ],
      },
      {
        name: "Leakages",
        icon: TrendingDown,
        subItems: [
          { name: "Dashboard", href: "/dashboard/leakages" },
          { name: "Browse", href: "/dashboard/leakages/browse" },
        ],
      },
      {
        name: "Settings",
        icon: Settings,
        href: "/dashboard/project-management/settings",
      },
    ],
  },
];

const adminSectionItems = [
  {
    name: "Dashboard",
    icon: LayoutDashboard,
    href: "/dashboard",
  },
  {
    name: "Marketplace",
    icon: ShoppingBag,
    href: "/dashboard/marketplace",
  },
  {
    name: "Settings",
    icon: Settings,
    href: "/dashboard/settings",
  },
];

const userSectionItems = [
  {
    name: "Dashboard",
    icon: LayoutDashboard,
    href: "/dashboard",
  },
  {
    name: "Marketplace",
    icon: ShoppingBag,
    href: "/dashboard/marketplace",
  },
  {
    name: "User Settings",
    icon: Settings,
    href: "/dashboard/user-settings",
  },
];

interface SidebarProps {
  session: Session | null;
}

export function Sidebar({ session }: SidebarProps) {
  const [openItems, setOpenItems] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const userRole = session?.user?.role as UserRole;
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsLoading(false);
    setIsClient(true);
  }, []);

  if (isLoading || !isClient) {
    return <div className="flex h-screen w-64 flex-col bg-gray-800" />;
  }

  const toggleItem = (item: string, sectionLabel: string) => {
    const itemKey = `${sectionLabel}-${item}`;
    setOpenItems((prev) =>
      prev.includes(itemKey) ? prev.filter((i) => i !== itemKey) : [...prev, itemKey]
    );
  };

  return (
    <div className="flex h-screen w-64 flex-col bg-gray-800 text-white">
      <div className="flex h-16 items-center justify-center border-b border-gray-700">
        <h1 className="text-xl font-bold">Rekover</h1>
      </div>
      <div className="flex-1 overflow-y-auto">
        <nav className="px-2 py-4">
          <ul className="space-y-4">
            {MenuItems.map((section) => (
              <li key={section.label}>
                <h2 className="mb-2 px-4 text-xs font-semibold uppercase tracking-wider text-gray-400">
                  {section.label}
                </h2>
                <ul className="space-y-1">
                  {section.items.map((item) => (
                    <li key={item.name}>
                      {item.subItems ? (
                        <Collapsible
                          key={`${section.label}-${item.name}`}
                          open={openItems.includes(`${section.label}-${item.name}`)}
                          onOpenChange={() => toggleItem(item.name, section.label)}
                        >
                          <CollapsibleTrigger asChild>
                            <Button
                              variant="ghost"
                              className="flex w-full items-center justify-between px-4 py-2 text-sm text-gray-300 hover:bg-gray-700"
                            >
                              <span className="flex items-center">
                                <item.icon className="mr-3 h-4 w-4" />
                                {item.name}
                              </span>
                              <ChevronDown
                                className={`h-4 w-4 transition-transform duration-200 ${
                                  openItems.includes(`${section.label}-${item.name}`)
                                    ? "rotate-180"
                                    : ""
                                }`}
                              />
                            </Button>
                          </CollapsibleTrigger>
                          <CollapsibleContent>
                            <ul className="ml-4 mt-1 space-y-1">
                              {item.subItems.map((subItem) => (
                                <li key={subItem.name}>
                                  <Link
                                    href={subItem.href}
                                    className="block rounded-md px-4 py-2 text-sm text-gray-400 hover:bg-gray-700 hover:text-white"
                                  >
                                    {subItem.name}
                                  </Link>
                                </li>
                              ))}
                            </ul>
                          </CollapsibleContent>
                        </Collapsible>
                      ) : (
                        <Link href={item.href}>
                          <Button
                            variant="ghost"
                            className="flex w-full items-center justify-between px-4 py-2 text-sm text-gray-300 hover:bg-gray-700"
                          >
                            <span className="flex items-center">
                              <item.icon className="mr-3 h-4 w-4" />
                              {item.name}
                            </span>
                          </Button>
                        </Link>
                      )}
                    </li>
                  ))}
                </ul>
              </li>
            ))}
          </ul>
        </nav>
      </div>
      <div className="border-t border-gray-700">
        <div className="px-4 py-4">
          <p className="text-sm font-medium text-gray-400 mb-2">User</p>
          <ul className="space-y-1">
            {userRole === "admin"
              ? adminSectionItems.map((item) => (
                  <li key={item.name}>
                    <Link href={item.href}>
                      <Button
                        variant="ghost"
                        className="flex w-full items-center justify-start px-2 py-2 text-sm text-gray-300 hover:bg-gray-700"
                      >
                        <item.icon className="mr-3 h-4 w-4" />
                        {item.name}
                      </Button>
                    </Link>
                  </li>
                ))
              : userSectionItems.map((item) => (
                  <li key={item.name}>
                    <Link href={item.href}>
                      <Button
                        variant="ghost"
                        className="flex w-full items-center justify-start px-2 py-2 text-sm text-gray-300 hover:bg-gray-700"
                      >
                        <item.icon className="mr-3 h-4 w-4" />
                        {item.name}
                      </Button>
                    </Link>
                  </li>
                ))}
            <li>
              <Button
                variant="ghost"
                onClick={() => signOut()}
                className="flex w-full items-center justify-start px-2 py-2 text-sm text-gray-300 hover:bg-gray-700"
              >
                <LogOut className="mr-3 h-4 w-4" />
                Logout
              </Button>
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
}
