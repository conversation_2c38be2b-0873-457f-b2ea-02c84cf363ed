"use client"

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Slider } from "@/components/ui/slider"
import { Loader2, Activity, MapPin } from 'lucide-react'
import { toast } from "@/components/ui/use-toast"
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, ReferenceLine } from 'recharts'
import { Alert, AlertDescription } from "@/components/ui/alert"

interface EarthquakeData {
  pga: number
  probability: number
}

interface EarthquakeResponse {
  data: Array<{
    pga_value: number
    probability_of_exceedance: number
  }>
  metadata?: {
    target_latitude: number
    target_longitude: number
    window: number
    vs30?: number
  }
}

interface LocationInfo {
  city?: string
  country?: string
  fullAddress?: string
  isLoading: boolean
  error?: string
}

interface AssetType {
  id: string
  name: string
  pgaTolerance: number
  description: string
}

const ASSET_TYPES: AssetType[] = [
  { id: 'residential_standard', name: 'Residential Building (Standard)', pgaTolerance: 0.20, description: 'Typical residential construction' },
  { id: 'residential_reinforced', name: 'Residential Building (Reinforced)', pgaTolerance: 0.35, description: 'Seismically reinforced residential' },
  { id: 'commercial_standard', name: 'Commercial Building (Standard)', pgaTolerance: 0.18, description: 'Standard commercial construction' },
  { id: 'commercial_seismic', name: 'Commercial Building (Seismic Design)', pgaTolerance: 0.40, description: 'Seismically designed commercial' },
  { id: 'industrial', name: 'Industrial Facility', pgaTolerance: 0.15, description: 'Industrial/manufacturing facility' },
  { id: 'critical', name: 'Critical Infrastructure', pgaTolerance: 0.12, description: 'Hospitals, emergency services' },
  { id: 'custom', name: 'Custom', pgaTolerance: 0, description: 'User-defined PGA tolerance' }
]

export function EarthquakePredictionTool() {
  const [formData, setFormData] = useState({
    latitude: '',
    longitude: '',
    window: '1',
    vs30: ''
  })
  const [interactiveData, setInteractiveData] = useState({
    assetValue: '500000',
    assetType: '',
    customPGA: '0.20'
  })
  const [isLoading, setIsLoading] = useState(false)
  const [chartData, setChartData] = useState<EarthquakeData[]>([])
  const [error, setError] = useState<string | null>(null)
  const [hasResults, setHasResults] = useState(false)
  const [locationInfo, setLocationInfo] = useState<LocationInfo>({ isLoading: false })

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
    setError(null)
  }

  const handleWindowChange = (value: string) => {
    setFormData(prev => ({ ...prev, window: value }))
  }

  const handleAssetTypeChange = (value: string) => {
    setInteractiveData(prev => ({ ...prev, assetType: value }))

    // Auto-fill PGA tolerance for predefined asset types
    const selectedAsset = ASSET_TYPES.find(asset => asset.id === value)
    if (selectedAsset && selectedAsset.id !== 'custom') {
      setInteractiveData(prev => ({ ...prev, customPGA: selectedAsset.pgaTolerance.toString() }))
    } else if (selectedAsset?.id === 'custom') {
      setInteractiveData(prev => ({ ...prev, customPGA: '' }))
    }
  }

  const handleInteractiveChange = (field: string, value: string) => {
    setInteractiveData(prev => ({ ...prev, [field]: value }))
  }

  // Utility function for reverse geocoding
  const fetchLocationInfo = async (lat: number, lng: number): Promise<LocationInfo> => {
    try {
      const response = await fetch(
        `https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&addressdetails=1`,
        {
          headers: {
            'User-Agent': 'Rekover-Earthquake-Tool/1.0'
          }
        }
      )

      if (!response.ok) {
        throw new Error('Geocoding service unavailable')
      }

      const data = await response.json()

      return {
        city: data.address?.city || data.address?.town || data.address?.village || data.address?.municipality,
        country: data.address?.country,
        fullAddress: data.display_name,
        isLoading: false
      }
    } catch (error) {
      return {
        isLoading: false,
        error: 'Unable to fetch location information'
      }
    }
  }

  // Utility function to interpolate probability at specific PGA
  const interpolateProbability = (targetPGA: number, data: EarthquakeData[]): number => {
    if (data.length === 0) return 0

    // Sort data by PGA
    const sortedData = [...data].sort((a, b) => a.pga - b.pga)

    // If target PGA is outside the range, return boundary values
    if (targetPGA <= sortedData[0].pga) return sortedData[0].probability
    if (targetPGA >= sortedData[sortedData.length - 1].pga) return sortedData[sortedData.length - 1].probability

    // Find the two points to interpolate between
    for (let i = 0; i < sortedData.length - 1; i++) {
      const current = sortedData[i]
      const next = sortedData[i + 1]

      if (targetPGA >= current.pga && targetPGA <= next.pga) {
        // Linear interpolation
        const ratio = (targetPGA - current.pga) / (next.pga - current.pga)
        return current.probability + ratio * (next.probability - current.probability)
      }
    }

    return 0
  }

  // Calculate risk assessment
  const calculateRiskAssessment = () => {
    if (!interactiveData.assetValue || !interactiveData.customPGA || chartData.length === 0) return null

    const assetValue = parseFloat(interactiveData.assetValue)
    const maxPGA = parseFloat(interactiveData.customPGA)
    const probability = interpolateProbability(maxPGA, chartData)
    const expectedLoss = (probability / 100) * assetValue

    let riskLevel: 'LOW' | 'MEDIUM' | 'HIGH'
    if (probability < 2) riskLevel = 'LOW'
    else if (probability < 10) riskLevel = 'MEDIUM'
    else riskLevel = 'HIGH'

    return {
      probability: probability.toFixed(1),
      expectedLoss: expectedLoss.toFixed(0),
      riskLevel,
      assetValue: assetValue.toFixed(0),
      maxPGA: maxPGA.toFixed(2),
      lossPercentage: ((expectedLoss / assetValue) * 100).toFixed(1)
    }
  }

  const validateInputs = () => {
    const lat = parseFloat(formData.latitude)
    const lng = parseFloat(formData.longitude)

    if (!formData.latitude || !formData.longitude) {
      return "Latitude and longitude are required"
    }

    if (isNaN(lat) || lat < -90 || lat > 90) {
      return "Latitude must be between -90 and 90 degrees"
    }

    if (isNaN(lng) || lng < -180 || lng > 180) {
      return "Longitude must be between -180 and 180 degrees"
    }

    if (formData.vs30 && (isNaN(parseFloat(formData.vs30)) || parseFloat(formData.vs30) <= 0)) {
      return "Vs30 must be a positive number"
    }



    return null
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    const validationError = validateInputs()
    if (validationError) {
      setError(validationError)
      return
    }

    setIsLoading(true)
    setError(null)
    setChartData([])
    setHasResults(false)

    try {
      const params = new URLSearchParams({
        target_latitude: formData.latitude,
        target_longitude: formData.longitude,
        window: formData.window
      })

      if (formData.vs30) {
        params.append('Vs30', formData.vs30)
      }

      const url = `/api/v1/earthquake-prediction?${params}`
      console.log('Making request to:', url)

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
      })

      console.log('Response status:', response.status)
      console.log('Response headers:', Object.fromEntries(response.headers.entries()))

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: 'Unknown error' }))
        console.error('API error response:', errorData)
        throw new Error(errorData.error || `API error: ${response.status} ${response.statusText}`)
      }

      const data = await response.json()
      console.log('API response data:', data)

      // Transform the API response to chart data format
      let transformedData: EarthquakeData[] = []

      if (typeof data === 'object' && data !== null && !Array.isArray(data)) {
        // API returns an object with PGA values in m/s² as keys and probabilities as values
        // Convert m/s² to g (1g = 9.81 m/s²)
        transformedData = Object.entries(data).map(([pgaStr, probability]) => ({
          pga: parseFloat(pgaStr) / 9.81, // Convert m/s² to g
          probability: (probability as number) * 100 // Convert to percentage
        }))
      } else if (Array.isArray(data)) {
        // If data is directly an array
        transformedData = data.map((item: any) => ({
          pga: item.pga_value || item.pga || item.x,
          probability: (item.probability_of_exceedance || item.probability || item.y) * 100
        }))
      } else if (data.data && Array.isArray(data.data)) {
        // If data is wrapped in a data property
        transformedData = data.data.map((item: any) => ({
          pga: item.pga_value || item.pga || item.x,
          probability: (item.probability_of_exceedance || item.probability || item.y) * 100
        }))
      }

      if (transformedData.length === 0) {
        throw new Error("No valid data received from API")
      }

      // Sort by PGA value for better chart visualization
      transformedData.sort((a, b) => a.pga - b.pga)

      setChartData(transformedData)
      setHasResults(true)

      // Fetch location information
      setLocationInfo({ isLoading: true })
      const locationData = await fetchLocationInfo(parseFloat(formData.latitude), parseFloat(formData.longitude))
      setLocationInfo(locationData)

      toast({
        title: "Success",
        description: `Generated earthquake prediction curve with ${transformedData.length} data points`,
      })

    } catch (err) {
      console.error('API Error:', err)

      let errorMessage = "Failed to fetch earthquake prediction data"

      if (err instanceof Error) {
        if (err.message.includes('Failed to fetch')) {
          errorMessage = "Network error: Unable to connect to the earthquake prediction service. This could be due to CORS restrictions, network connectivity issues, or the API being temporarily unavailable."
        } else if (err.message.includes('CORS')) {
          errorMessage = "CORS error: The earthquake prediction API doesn't allow requests from this domain. This is a server-side configuration issue."
        } else if (err.message.includes('timeout')) {
          errorMessage = "Request timeout: The earthquake prediction service is taking too long to respond. Please try again."
        } else {
          errorMessage = err.message
        }
      }

      setError(errorMessage)

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleReset = () => {
    setFormData({
      latitude: '',
      longitude: '',
      window: '1',
      vs30: ''
    })
    setInteractiveData({
      assetValue: '500000',
      assetType: '',
      customPGA: '0.20'
    })
    setChartData([])
    setError(null)
    setHasResults(false)
    setLocationInfo({ isLoading: false })
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Earthquake Prediction Analysis
          </CardTitle>
          <CardDescription>
            Generate probabilistic earthquake curves and assess financial risk for insurance underwriting
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="latitude">Latitude *</Label>
                <Input
                  id="latitude"
                  name="latitude"
                  type="number"
                  step="any"
                  min="-90"
                  max="90"
                  value={formData.latitude}
                  onChange={handleInputChange}
                  placeholder="e.g., -15.02"
                  required
                />
                <p className="text-xs text-muted-foreground">Range: -90 to 90 degrees</p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="longitude">Longitude *</Label>
                <Input
                  id="longitude"
                  name="longitude"
                  type="number"
                  step="any"
                  min="-180"
                  max="180"
                  value={formData.longitude}
                  onChange={handleInputChange}
                  placeholder="e.g., -72.15"
                  required
                />
                <p className="text-xs text-muted-foreground">Range: -180 to 180 degrees</p>
              </div>
            </div>

            <div className="space-y-3">
              <Label>Prediction Window *</Label>
              <RadioGroup value={formData.window} onValueChange={handleWindowChange}>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="1" id="window-1" />
                  <Label htmlFor="window-1">1 Year</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="3" id="window-3" />
                  <Label htmlFor="window-3">3 Years</Label>
                </div>
              </RadioGroup>
            </div>

            <div className="space-y-2">
              <Label htmlFor="vs30">Vs30 (Optional)</Label>
              <Input
                id="vs30"
                name="vs30"
                type="number"
                step="any"
                min="0"
                value={formData.vs30}
                onChange={handleInputChange}
                placeholder="e.g., 760"
              />
              <p className="text-xs text-muted-foreground">
                Leave empty to use default USGS values
              </p>
            </div>



            {error && (
              <Alert variant="destructive">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <div className="flex gap-3">
              <Button type="submit" disabled={isLoading} className="flex-1">
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Generating Prediction...
                  </>
                ) : (
                  "Generate Prediction"
                )}
              </Button>
              <Button type="button" variant="outline" onClick={handleReset}>
                Reset
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>

      {hasResults && chartData.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Earthquake Prediction Curve</CardTitle>
            <CardDescription>
              Probability of exceedance vs Peak Ground Acceleration (PGA) for {formData.window} year{formData.window === '1' ? '' : 's'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-96 w-full">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={chartData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis
                    dataKey="pga"
                    label={{ value: 'PGA (g)', position: 'insideBottom', offset: -5 }}
                    tickFormatter={(value) => Number(value).toFixed(0)}
                    type="number"
                    scale="linear"
                  />
                  <YAxis
                    label={{ value: 'Probability of Exceedance (%)', angle: -90, position: 'insideLeft' }}
                    tickFormatter={(value) => `${Number(value).toFixed(1)}%`}
                  />
                  <Tooltip
                    formatter={(value, name) => [
                      `${Number(value).toFixed(2)}${name === 'probability' ? '%' : 'g'}`,
                      name === 'probability' ? 'Probability of Exceedance' : 'PGA'
                    ]}
                    labelFormatter={(label) => `PGA: ${Number(label).toFixed(0)}g`}
                  />
                  {/* Reference line for PGA threshold */}
                  {interactiveData.customPGA && parseFloat(interactiveData.customPGA) > 0 && (
                    <ReferenceLine
                      x={parseFloat(interactiveData.customPGA)}
                      stroke="#ef4444"
                      strokeWidth={2}
                      strokeDasharray="5 5"
                      label={{ value: `Max PGA: ${interactiveData.customPGA}g`, position: "topRight" }}
                    />
                  )}
                  <Line
                    type="monotone"
                    dataKey="probability"
                    stroke="#2563eb"
                    strokeWidth={2}
                    dot={{ fill: '#2563eb', strokeWidth: 2, r: 3 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
            <div className="mt-4 text-sm text-muted-foreground">
              <div className="flex items-center gap-2 mb-2">
                <MapPin className="h-4 w-4" />
                <span>
                  <strong>Location:</strong>{' '}
                  {locationInfo.isLoading ? (
                    <span className="inline-flex items-center gap-1">
                      <Loader2 className="h-3 w-3 animate-spin" />
                      Loading location...
                    </span>
                  ) : locationInfo.city && locationInfo.country ? (
                    `${locationInfo.city}, ${locationInfo.country} (${formData.latitude}°, ${formData.longitude}°)`
                  ) : locationInfo.error ? (
                    `${formData.latitude}°, ${formData.longitude}° (Location unavailable)`
                  ) : (
                    `${formData.latitude}°, ${formData.longitude}°`
                  )}
                </span>
              </div>
              <p>
                <strong>Analysis Window:</strong> {formData.window} year{formData.window === '1' ? '' : 's'}
                {formData.vs30 && <span> | <strong>Vs30:</strong> {formData.vs30}</span>}
                {interactiveData.customPGA && <span> | <strong>PGA Threshold:</strong> {interactiveData.customPGA}g</span>}
              </p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Interactive Risk Assessment Controls */}
      {hasResults && chartData.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Interactive Risk Assessment</CardTitle>
            <CardDescription>
              Adjust asset value and PGA tolerance to see real-time risk calculations
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {/* Asset Value Slider */}
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <Label htmlFor="assetValueSlider">Asset Value (€)</Label>
                  <span className="text-sm font-medium">
                    €{interactiveData.assetValue ? parseInt(interactiveData.assetValue).toLocaleString() : '0'}
                  </span>
                </div>
                <Slider
                  id="assetValueSlider"
                  min={50000}
                  max={5000000}
                  step={50000}
                  value={[parseInt(interactiveData.assetValue) || 500000]}
                  onValueChange={(value) => handleInteractiveChange('assetValue', value[0].toString())}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>€50K</span>
                  <span>€5M</span>
                </div>
              </div>

              {/* Asset Type Selection */}
              <div className="space-y-2">
                <Label htmlFor="assetTypeSelect">Asset Type</Label>
                <Select value={interactiveData.assetType} onValueChange={handleAssetTypeChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select asset type" />
                  </SelectTrigger>
                  <SelectContent>
                    {ASSET_TYPES.map((asset) => (
                      <SelectItem key={asset.id} value={asset.id}>
                        <div className="flex flex-col">
                          <span>{asset.name}</span>
                          <span className="text-xs text-muted-foreground">{asset.description}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* PGA Tolerance Slider */}
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <Label htmlFor="pgaSlider">Maximum Tolerable PGA (g)</Label>
                  <span className="text-sm font-medium">
                    {interactiveData.customPGA ? parseFloat(interactiveData.customPGA).toFixed(2) : '0.00'}g
                  </span>
                </div>
                <Slider
                  id="pgaSlider"
                  min={0.05}
                  max={0.50}
                  step={0.01}
                  value={[parseFloat(interactiveData.customPGA) || 0.15]}
                  onValueChange={(value) => handleInteractiveChange('customPGA', value[0].toFixed(2))}
                  className="w-full"
                  disabled={interactiveData.assetType && interactiveData.assetType !== 'custom'}
                />
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>0.05g</span>
                  <span>0.50g</span>
                </div>
                {interactiveData.assetType && interactiveData.assetType !== 'custom' && (
                  <p className="text-xs text-muted-foreground">
                    PGA value is auto-set based on selected asset type. Select "Custom" to adjust manually.
                  </p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Risk Assessment Card */}
      {hasResults && interactiveData.assetValue && interactiveData.customPGA && (() => {
        const riskData = calculateRiskAssessment()
        return riskData ? (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Seismic Risk Assessment
              </CardTitle>
              <CardDescription>
                Financial risk analysis based on asset value and PGA tolerance
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {/* Risk Summary */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center p-4 bg-muted rounded-lg">
                    <div className="text-2xl font-bold text-primary">{riskData.probability}%</div>
                    <div className="text-sm text-muted-foreground">Damage Probability</div>
                  </div>
                  <div className="text-center p-4 bg-muted rounded-lg">
                    <div className="text-2xl font-bold text-primary">€{parseInt(riskData.expectedLoss).toLocaleString()}</div>
                    <div className="text-sm text-muted-foreground">Expected Loss</div>
                  </div>
                  <div className="text-center p-4 bg-muted rounded-lg">
                    <div className={`text-2xl font-bold ${
                      riskData.riskLevel === 'LOW' ? 'text-green-600' :
                      riskData.riskLevel === 'MEDIUM' ? 'text-yellow-600' : 'text-red-600'
                    }`}>
                      {riskData.riskLevel}
                    </div>
                    <div className="text-sm text-muted-foreground">Risk Level</div>
                  </div>
                </div>

                {/* Detailed Assessment */}
                <div className="space-y-4 p-4 bg-muted/50 rounded-lg">
                  <h4 className="font-semibold text-lg">SEISMIC RISK ASSESSMENT</h4>

                  <div className="space-y-2">
                    <h5 className="font-medium">Asset Details:</h5>
                    <ul className="space-y-1 text-sm">
                      <li>
                        <strong>Location:</strong>{' '}
                        {locationInfo.city && locationInfo.country
                          ? `${locationInfo.city}, ${locationInfo.country} (${formData.latitude}°, ${formData.longitude}°)`
                          : `${formData.latitude}°, ${formData.longitude}°`
                        }
                      </li>
                      <li><strong>Asset Value:</strong> €{parseInt(riskData.assetValue).toLocaleString()}</li>
                      <li><strong>PGA Tolerance:</strong> {riskData.maxPGA}g</li>
                      <li><strong>Time Window:</strong> {formData.window} year{formData.window === '1' ? '' : 's'}</li>
                    </ul>
                  </div>

                  <div className="space-y-2">
                    <h5 className="font-medium">Risk Analysis:</h5>
                    <ul className="space-y-1 text-sm">
                      <li><strong>Probability of Damage:</strong> {riskData.probability}%</li>
                      <li><strong>Expected Loss:</strong> €{parseInt(riskData.expectedLoss).toLocaleString()} ({riskData.lossPercentage}% of asset value)</li>
                      <li><strong>Risk Level:</strong> {riskData.riskLevel}</li>
                    </ul>
                  </div>

                  <div className="mt-4 p-3 bg-background rounded border-l-4 border-l-primary">
                    <p className="text-sm">
                      <strong>Assessment Summary:</strong> There is a {riskData.probability}% chance that seismic activity exceeding {riskData.maxPGA}g could damage your €{parseInt(riskData.assetValue).toLocaleString()} asset within the next {formData.window} year{formData.window === '1' ? '' : 's'}, potentially resulting in losses up to €{parseInt(riskData.expectedLoss).toLocaleString()}.
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ) : null
      })()}
    </div>
  )
}
